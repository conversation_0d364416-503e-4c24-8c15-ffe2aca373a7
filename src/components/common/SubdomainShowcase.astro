---
// SubdomainShowcase.astro - 展示 moatkon.com 的子域名和主要内容
---

<!-- 主要内容区域 -->
<div class="main-content">
  <div class="card-grid">
    <a href="/software-engineer/readme" class="card engineer">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="2"/>
        </svg>
      </div>
      <div class="card-content">
        <h3>软件工程师</h3>
        <p>优雅且高效地处理数据</p>
      </div>
      <div class="card-arrow">→</div>
    </a>

    <a href="/english" class="card english">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" fill="currentColor" fill-opacity="0.1"/>
        </svg>
      </div>
      <div class="card-content">
        <h3>英语</h3>
        <p>最好的语言不是Java、C++之类的,而是英语</p>
      </div>
      <div class="card-arrow">→</div>
    </a>

    <a href="/music/piano/study" class="card piano">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <rect x="6" y="5" width="12" height="14" stroke="currentColor" stroke-width="2"/>
          <line x1="10" y1="5" x2="10" y2="19" stroke="currentColor" stroke-width="2"/>
          <line x1="14" y1="5" x2="14" y2="19" stroke="currentColor" stroke-width="2"/>
          <line x1="8" y1="5" x2="8" y2="12" stroke="currentColor" stroke-width="1.5"/>
          <line x1="12" y1="5" x2="12" y2="12" stroke="currentColor" stroke-width="1.5"/>
          <line x1="16" y1="5" x2="16" y2="12" stroke="currentColor" stroke-width="1.5"/>
        </svg>
      </div>
      <div class="card-content">
        <h3>钢琴🎹</h3>
        <p>因为我熟悉键盘,所以我选择了带按键的乐器</p>
      </div>
      <div class="card-arrow">→</div>
    </a>

    <a href="/share" class="card share">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <circle cx="18" cy="5" r="3" stroke="currentColor" stroke-width="2"/>
          <circle cx="6" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
          <circle cx="18" cy="19" r="3" stroke="currentColor" stroke-width="2"/>
          <path d="M8.59 13.51L15.42 17.49M15.41 6.51L8.59 10.49" stroke="currentColor" stroke-width="2"/>
        </svg>
      </div>
      <div class="card-content">
        <h3>分享</h3>
        <p>效率工具、博主、电影🎬、视频、网站、音乐🎵</p>
      </div>
      <div class="card-arrow">→</div>
    </a>

    <a href="/ai" class="card ai">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2"/>
          <path d="M2 17L12 22L22 17M2 12L12 17L22 12" stroke="currentColor" stroke-width="2"/>
        </svg>
      </div>
      <div class="card-content">
        <h3>AI</h3>
        <p>Artificial intelligence,智能或者说智慧,我理解的就是概率</p>
      </div>
      <div class="card-arrow">→</div>
    </a>
  </div>
</div>

<!-- 子域名展示 -->
<div class="subdomain-section">
  <div class="section-header">
    <h2><span class="brand">moatkon</span></h2>
  </div>
  
  <div class="card-grid">
    <a href="https://blog.moatkon.com" class="card blog" target="_blank" rel="noopener">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2"/>
          <path d="M9 9H15M9 13H15M9 17H13" stroke="currentColor" stroke-width="2"/>
        </svg>
      </div>
      <div class="card-content">
        <h3>Blog</h3>
        <p>记录生活与感悟</p>
        <span class="card-url">blog.moatkon.com</span>
      </div>
      <div class="card-arrow">→</div>
    </a>

    <a href="https://resume.moatkon.com" class="card resume" target="_blank" rel="noopener">
      <div class="card-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z" stroke="currentColor" stroke-width="2"/>
          <path d="M14 2v6h6M16 13H8M16 17H8M10 9H8" stroke="currentColor" stroke-width="2"/>
        </svg>
      </div>
      <div class="card-content">
        <h3>Resume</h3>
        <p>专业履历与技能展示</p>
        <span class="card-url">resume.moatkon.com</span>
      </div>
      <div class="card-arrow">→</div>
    </a>
  </div>
</div>

<!-- 智慧语录 -->
<div class="quotes-section">
  <div class="section-header">
    <h2>智慧语录</h2>
    <p>生活中的感悟与思考</p>
  </div>

  <div class="quotes-grid">
    <div class="quote-card growth">
      <div class="quote-header">
        <div class="quote-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
            <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z" fill="currentColor"/>
          </svg>
        </div>
        <div class="quote-meta">
          <span class="quote-category">成长思考</span>
        </div>
      </div>
      <p class="quote-text">
        "Does this help growth or not?"
      </p>
      <p class="quote-translation">"这是否有助于生长？"</p>
    </div>

    <div class="quote-card wisdom">
      <div class="quote-header">
        <div class="quote-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
            <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z" fill="currentColor"/>
          </svg>
        </div>
        <div class="quote-meta">
          <span class="quote-category">人生智慧</span>
        </div>
      </div>
      <p class="quote-text">
        "生存法则很简单，就是忍人所不忍，能人所不能。忍是一条线，能是一条线，两者的间距就是生存机会。"
      </p>
      <p class="quote-translation" style="visibility: hidden; height: 0; margin: 0;">&nbsp;</p>
    </div>
  </div>
</div>

</div>

<style>
  /* 基础变量 */
  :root {
    --primary-blue: #3b82f6;
    --primary-orange: #f97316;
    --primary-purple: #a855f7;
    --primary-green: #10b981;
    --primary-red: #ef4444;
    --primary-magenta: #ec4899;
    
    --bg-primary: var(--sl-color-gray-7, var(--sl-color-gray-6));
    --text-primary: var(--sl-color-text);
    --text-secondary: var(--sl-color-gray-3);
    --border-light: var(--sl-color-hairline-light);
  }

  /* 通用样式 */
  .card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 0.75rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
  }

  .card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--card-color);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .card:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--card-color);
  }

  .card:hover::before {
    opacity: 1;
  }

  .card-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: color-mix(in srgb, var(--card-color) 10%, transparent);
    border-radius: 0.5rem;
    color: var(--card-color);
    transition: all 0.3s ease;
    flex-shrink: 0;
  }

  .card:hover .card-icon {
    background: var(--card-color);
    color: white;
    transform: scale(1.1);
  }

  .card-content {
    flex: 1;
    min-width: 0;
  }

  .card-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
    transition: color 0.3s ease;
  }

  .card:hover .card-content h3 {
    color: var(--card-color);
  }

  .card-content p {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.4;
  }

  .card-url {
    font-size: 0.75rem;
    color: var(--card-color);
    font-weight: 500;
    font-family: monospace;
    margin-top: 0.25rem;
    display: block;
  }

  .card-arrow {
    font-size: 1.2rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    flex-shrink: 0;
  }

  .card:hover .card-arrow {
    color: var(--card-color);
    transform: translateX(2px);
  }

  /* 卡片主题色 */
  .engineer { --card-color: var(--primary-blue); }
  .english { --card-color: var(--primary-orange); }
  .piano { --card-color: var(--primary-magenta); }
  .share { --card-color: var(--primary-green); }
  .ai { --card-color: var(--primary-purple); }
  .blog { --card-color: var(--primary-blue); }
  .resume { --card-color: var(--primary-green); }
  .growth { --card-color: var(--primary-orange); }
  .wisdom { --card-color: var(--primary-red); }

  /* 区域样式 */
  .main-content, .subdomain-section, .quotes-section {
    margin-bottom: 3rem;
  }

  .subdomain-section, .quotes-section {
    padding-top: 2rem;
    border-top: 1px solid var(--border-light);
  }

  .section-header {
    text-align: center;
    margin-bottom: 2rem;
  }

  .section-header h2 {
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
  }

  .section-header p {
    color: var(--text-secondary);
    margin: 0;
  }

  .brand {
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    font-weight: 800;
  }

  /* 智慧语录样式 */
  .quotes-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 0;
    align-items: flex-start; /* 强制顶部对齐 */
  }

  .quotes-grid > .quote-card {
    flex: 1;
    min-width: 400px;
  }

  .quote-card {
    padding: 2.5rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 0.5rem;
    position: relative;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    width: 100%;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    align-self: start; /* 强制从顶部开始 */
    display: flex;
    flex-direction: column;
  }

  .quote-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--quote-color), color-mix(in srgb, var(--quote-color) 70%, transparent));
  }

  .quote-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-color: var(--quote-color);
  }

  .quote-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    height: 48px; /* 固定高度确保对齐 */
    flex-shrink: 0; /* 防止压缩 */
  }

  .quote-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: color-mix(in srgb, var(--quote-color) 12%, transparent);
    border-radius: 12px;
    color: var(--quote-color);
    transition: all 0.3s ease;
    opacity: 0.8;
  }

  .quote-card:hover .quote-icon {
    background: var(--quote-color);
    color: white;
    transform: scale(1.05);
    opacity: 1;
  }

  .quote-meta {
    text-align: right;
  }

  .quote-category {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--quote-color);
    background: color-mix(in srgb, var(--quote-color) 10%, transparent);
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .quote-text {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 1rem 0;
    line-height: 1.6;
    font-style: normal;
    position: relative;
    padding: 0;
    quotes: """ """ "'" "'";
    flex-shrink: 0; /* 防止压缩 */
  }

  .quote-text::before {
    content: open-quote;
    font-size: 3rem;
    color: var(--quote-color);
    opacity: 0.3;
    position: absolute;
    left: -0.5rem;
    top: -0.8rem;
    line-height: 1;
    font-family: Georgia, serif;
  }

  .quote-translation {
    font-size: 1rem;
    color: var(--quote-color);
    margin: 0 0 1.25rem 0;
    font-style: italic;
    font-weight: 500;
  }

  .quote-description {
    font-size: 0.95rem;
    color: var(--text-secondary);
    margin: 0 0 1.5rem 0;
    line-height: 1.7;
  }

  .quote-source {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    padding-top: 1rem;
    border-top: 1px solid color-mix(in srgb, var(--border-light) 50%, transparent);
  }

  .quote-source span {
    color: var(--text-secondary);
    font-weight: 500;
  }

  .quote-source a {
    color: var(--quote-color);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
  }

  .quote-source a:hover {
    text-decoration: underline;
    color: color-mix(in srgb, var(--quote-color) 80%, black);
  }

  /* 语录主题色 */
  .wisdom { --quote-color: var(--primary-red); }
  .growth { --quote-color: var(--primary-orange); }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .card-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .card {
      padding: 1.25rem;
    }

    .brand {
      font-size: 1.75rem;
    }

    .section-header h2 {
      font-size: 1.5rem;
    }

    .quotes-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
      padding: 0;
      margin: 0;
      width: 100%;
    }

    .quote-card {
      padding: 2rem;
      margin: 0;
      width: 100%;
    }

    .quote-text {
      font-size: 1.1rem;
    }

    .quote-text::before {
      font-size: 2.5rem;
      left: -0.3rem;
      top: -0.6rem;
    }

    .quotes-section {
      padding: 2rem 0 0 0;
      margin: 0;
      width: 100%;
      overflow: hidden;
    }
  }
</style>